"""
Campaign Reporting - Layered Architecture Entry Point

This is the new main entry point for the refactored campaign reporting system.
"""
import asyncio
import sys
import os
import argparse
from datetime import datetime, timedelta

# Add the current directory to the path so we can import modules
sys.path.append(os.path.dirname(__file__))

import logging_config
from campaign_reporting.presentation.main_controller import run_campaign_reporting

# Setup logging
logger = logging_config.setup_logging(__name__)


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Campaign Reporting System",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python campaign_reporting_main.py --locale pt_BR --merchants 123,456,789 --sellers_id 111,222,333
  python campaign_reporting_main.py --locale es_AR --merchants 123,456,789 --html
  python campaign_reporting_main.py --locale pt_BR --merchants 123,456 --html --date_from 2024-01-01 --date_to 2024-01-31
        """
    )

    parser.add_argument(
        '--locale',
        type=str,
        default='pt_BR',
        choices=['pt_BR', 'es_AR'],
        help='Locale for the reports (default: pt_BR). Accepts pt_BR or es_AR'
    )

    parser.add_argument(
        '--merchants',
        type=str,
        required=True,
        help='Comma-separated merchant IDs'
    )

    parser.add_argument(
        '--sellers_id',
        type=str,
        default=None,
        help='Comma-separated seller IDs (optional)'
    )

    parser.add_argument(
        '--html',
        action='store_true',
        help='Generate HTML files in addition to PDF files (default: False)'
    )

    parser.add_argument(
        '--date_from',
        type=str,
        default=None,
        help='Start date for data collection in YYYY-MM-DD format (default: 60 days ago)'
    )

    parser.add_argument(
        '--date_to',
        type=str,
        default=None,
        help='End date for data collection in YYYY-MM-DD format (default: today)'
    )

    return parser.parse_args()


def parse_ids_input(input_str):
    """
    Parse input string as either comma-separated IDs or file path.

    Args:
        input_str: String containing either comma-separated IDs or file path

    Returns:
        List of IDs
    """
    if not input_str:
        return []

    return [id_str.strip() for id_str in input_str.split(',') if id_str.strip()]


def validate_and_set_dates(date_from_str, date_to_str):
    """
    Validate and set default dates if not provided.

    Args:
        date_from_str: Start date string in YYYY-MM-DD format or None
        date_to_str: End date string in YYYY-MM-DD format or None

    Returns:
        Tuple of (date_from, date_to) as strings in YYYY-MM-DD format

    Raises:
        ValueError: If date format is invalid or date_from > date_to
    """
    date_format = "%Y-%m-%d"

    # Set defaults if not provided
    if not date_to_str:
        date_to = datetime.now().strftime(date_format)
    else:
        try:
            datetime.strptime(date_to_str, date_format)
            date_to = date_to_str
        except ValueError:
            raise ValueError(f"Invalid date_to format: {date_to_str}. Expected YYYY-MM-DD")

    if not date_from_str:
        date_from = (datetime.now() - timedelta(days=30)).strftime(date_format)
    else:
        try:
            datetime.strptime(date_from_str, date_format)
            date_from = date_from_str
        except ValueError:
            raise ValueError(f"Invalid date_from format: {date_from_str}. Expected YYYY-MM-DD")

    # Validate that date_from <= date_to
    if datetime.strptime(date_from, date_format) > datetime.strptime(date_to, date_format):
        raise ValueError(f"date_from ({date_from}) cannot be later than date_to ({date_to})")

    return date_from, date_to


async def main():
    """Main entry point for the campaign reporting application."""
    try:
        # Parse command line arguments
        args = parse_arguments()

        # Use locale directly
        locale = args.locale

        # Parse merchant and seller IDs
        merchant_ids = parse_ids_input(args.merchants)
        seller_ids = parse_ids_input(args.sellers_id) if args.sellers_id else []

        if not merchant_ids:
            logger.error("No valid merchant IDs provided")
            sys.exit(1)

        # Validate and set date range
        try:
            date_from, date_to = validate_and_set_dates(args.date_from, args.date_to)
        except ValueError as e:
            logger.error(f"Date validation error: {e}")
            sys.exit(1)

        logger.info("Starting Campaign Reporting Application")
        logger.info(f"Locale: {locale}")
        logger.info(f"Merchant IDs: {len(merchant_ids)} merchants")
        logger.info(f"Seller IDs: {len(seller_ids)} sellers")
        logger.info(f"Generate HTML: {args.html}")
        logger.info(f"Date range: {date_from} to {date_to}")

        await run_campaign_reporting(
            locale=locale,
            merchant_ids=merchant_ids,
            seller_ids=seller_ids,
            generate_html=args.html,
            date_from=date_from,
            date_to=date_to
        )

        logger.info("Campaign Reporting Application completed successfully")
    except Exception as e:
        logger.error(f"Campaign Reporting Application failed: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
