"""
Recommendation Service

Handles product recommendation business logic for campaigns.
"""
import logging
import sys
import os

# Add the project root to the path so we can import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from input_data import build_output, calculate_metrics
from recommendation_report import filter_input
from campaign_reporting.data.recommendation_data_service import (
    fetch_merchant_campaigns_and_products,
    create_recommendation_report
)

logger = logging.getLogger(__name__)


async def process_merchant_recommendations(session, merchant_info, locale="pt_BR", campaign_products=None, date_from=None, date_to=None):
    """
    Process product recommendations for a merchant.

    Args:
        session: HTTP session for API calls
        merchant_info: Dictionary containing merchant and access_token
        locale: Language/locale for reports (default: pt_BR)
        campaign_products: Pre-fetched campaign products (optional, will fetch if not provided)
        date_from: Start date for data collection in YYYY-MM-DD format (optional)
        date_to: End date for data collection in YYYY-MM-DD format (optional)

    Returns:
        Boolean indicating success/failure
    """
    merchant = merchant_info['merchant']
    access_token = merchant_info['access_token']

    # Extract user_id from access_token for MercadoLibreClient
    user_id = access_token.split('-')[-1]
    user_id = int(user_id)

    logger.info(f"Processing recommendations for merchant {merchant}")

    try:
        # Use pre-fetched campaign products or fetch them if not provided
        if campaign_products is None:
            logger.info(f"Fetching campaign products for merchant {merchant}")
            campaign_products = await fetch_merchant_campaigns_and_products(
                access_token,
                user_id,
                merchant,
                date_from,
                date_to
            )

            if campaign_products is None:
                logger.warning(f"Could not fetch campaign data for merchant {merchant}")
                return False
        else:
            logger.info(f"Using pre-fetched campaign products for merchant {merchant} ({len(campaign_products)} products)")

        # Ensure campaign_products is a list
        if not isinstance(campaign_products, list):
            campaign_products = []

        # Build product data for analysis
        logger.info(f"Building product data for merchant {merchant}")
        df = await build_output(session, user_id, access_token, 30)

        if df.shape[0] == 0:
            logger.warning(f"No product data available for merchant {merchant}")
            return False

        # Process and analyze product data
        df = calculate_metrics(df)
        df['quality_score'] = df['quality_score'].astype('Int64')
        df['position'] = df['position'].astype('Int64')
        df = filter_input(df)

        # Find unlisted recommended items
        unlisted_items = _find_unlisted_recommended_items(
            df,
            campaign_products,
            merchant
        )

        if unlisted_items:
            # Generate recommendation report
            df_unlisted = df[df['item_id'].isin(unlisted_items)]
            await create_recommendation_report(df_unlisted, merchant, locale)
            logger.info(f"Generated recommendation report for merchant {merchant}")
            return True
        else:
            # Create empty report to show "no products found" message
            if not df.empty:
                empty_df = df.head(0)
                empty_df.loc[0, 'store_name'] = df['store_name'].iloc[0]
                empty_df.loc[0, 'store_permalink'] = df['store_permalink'].iloc[0]
                await create_recommendation_report(empty_df, merchant, locale)
                logger.info(f"Generated empty recommendation report for merchant {merchant}")
            return True

    except Exception as e:
        logger.error(f"Error processing recommendations for merchant {merchant}: {e}")
        return False


def _find_unlisted_recommended_items(df, campaign_products, merchant):
    """
    Find products that should be recommended but are not currently in campaigns.

    Args:
        df: DataFrame with product data
        campaign_products: List of products already in campaigns
        merchant: Merchant identifier for logging

    Returns:
        List of recommended item IDs not in campaigns
    """
    # Get recommended products (product_group == 2)
    df_rec = df[df['product_group'] == 2]

    # Remove products with NaN sales_potential
    df_rec = df_rec[~df_rec['sales_potential'].isna()]

    # Get list of items already in campaigns
    campaign_item_ids = [campaign['item_id'] for campaign in campaign_products]

    # Minimum number of items we want to find
    min_items_required = 3
    max_recommended_items = 8

    # Initialize unlisted_recommended_items
    unlisted_recommended_items = set()

    # Check if we have enough recommended items
    if len(df_rec['item_id'].tolist()) > min_items_required:
        unlisted_recommended_items = set(df_rec['item_id'].tolist()) - set(campaign_item_ids)
    else:
        # Try to find more items using A-class products
        unlisted_recommended_items = _find_additional_recommended_items(
            df, df_rec, campaign_item_ids, min_items_required, merchant
        )

    # Limit to maximum recommended items
    if unlisted_recommended_items:
        unlisted_recommended_items = list(unlisted_recommended_items)[:max_recommended_items]
        logger.info(f"Found {len(unlisted_recommended_items)} unlisted recommended items for merchant {merchant}")
    else:
        logger.warning(f"Could not find any unlisted recommended items for merchant {merchant}")

    return unlisted_recommended_items


def _find_additional_recommended_items(df, df_rec, campaign_item_ids, min_items_required, merchant):
    """
    Find additional recommended items by expanding to A-class products.

    Args:
        df: Full DataFrame with product data
        df_rec: DataFrame with initially recommended products
        campaign_item_ids: List of item IDs already in campaigns
        min_items_required: Minimum number of items needed
        merchant: Merchant identifier for logging

    Returns:
        Set of recommended item IDs
    """
    max_head = 3
    max_attempts = 5
    attempts = 0
    unlisted_recommended_items = set()

    a_class_items_count = df[df['abc_class'] == "A"].shape[0]

    while (len(unlisted_recommended_items) < min_items_required and
           attempts < max_attempts and
           max_head <= a_class_items_count):

        logger.info(f"Trying with max_head = {max_head} for merchant {merchant}")

        # Get top A-class items
        top_abc_items = df[df['abc_class'] == "A"].head(max_head)

        # Combine recommended items with top A-class items
        recommended_items = df_rec['item_id'].tolist() + top_abc_items['item_id'].tolist()

        # Find items not in campaigns
        unlisted_recommended_items = set(recommended_items) - set(campaign_item_ids)

        logger.info(f"Found {len(unlisted_recommended_items)} unlisted items, need at least {min_items_required}")

        if len(unlisted_recommended_items) < min_items_required:
            max_head *= 2  # Double max_head for next attempt
            attempts += 1

    # Log warning if we couldn't find enough items
    if len(unlisted_recommended_items) < min_items_required:
        logger.warning(f"Only found {len(unlisted_recommended_items)} items for merchant {merchant}, which is less than the minimum required ({min_items_required})")

    return unlisted_recommended_items
