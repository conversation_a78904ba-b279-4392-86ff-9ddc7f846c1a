"""
Campaign Data Service

Handles all campaign-related data access operations.
"""
import logging
import sys
import os
from typing import Optional

# Add the project root to the path so we can import services
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from services.mercadolibre_service import MercadoLibreClient
from campaign_reporting.config.settings import DATE_FROM_TWO_MONTHS, DATE_TO, DATE_FROM

logger = logging.getLogger(__name__)


async def fetch_advertiser_campaigns(access_token: str, user_id: int, date_from: Optional[str] = None, date_to: Optional[str] = None):
    """
    Fetch all campaigns for an advertiser.

    Args:
        access_token: MercadoLibre API access token
        user_id: User ID for API calls
        date_from: Start date for data collection in YYYY-MM-DD format (optional, will use default if not provided)
        date_to: End date for data collection in YYYY-MM-DD format (optional, will use default if not provided)

    Returns:
        List of campaigns or None if error
    """
    # Use provided dates or fall back to defaults
    start_date = date_from if date_from else DATE_FROM_TWO_MONTHS
    end_date = date_to if date_to else DATE_TO

    async with MercadoLibreClient(access_token, user_id) as client:
        advertiser_id = await client.get_advertiser_id_pads()
        if not advertiser_id:
            logger.error(f"Could not get advertiser ID for user {user_id}")
            return None

        campaigns = await client.list_advertiser_campaigns(
            advertiser_id,
            start_date,
            end_date
        )

        if not campaigns:
            logger.error(f"No campaigns found for user {user_id}")
            return None

        return campaigns


async def fetch_campaign_details(access_token: str, user_id: int, campaign_id: str, date_created: str, date_from: Optional[str] = None, date_to: Optional[str] = None):
    """
    Fetch detailed campaign data.

    Args:
        access_token: MercadoLibre API access token
        user_id: User ID for API calls
        campaign_id: Campaign ID to fetch
        date_created: Campaign creation date
        date_from: Start date for data collection in YYYY-MM-DD format (optional, will use default if not provided)
        date_to: End date for data collection in YYYY-MM-DD format (optional, will use default if not provided)

    Returns:
        Campaign data or None if error
    """
    # Use provided dates or fall back to defaults
    start_date = date_from if date_from else DATE_FROM_TWO_MONTHS
    end_date = date_to if date_to else DATE_TO

    async with MercadoLibreClient(access_token, user_id) as client:
        # Adjust date if needed
        if date_created.split("-")[0] != '2025':
            date_created = start_date

        campaign_data = await client.fetch_campaign_data(
            campaign_id,
            date_created.split("T")[0],
            end_date
        )

        if not campaign_data:
            logger.error(f"Could not fetch data for campaign {campaign_id}")
            return None

        return campaign_data


async def fetch_campaign_products(access_token: str, user_id: int, advertiser_id: str, campaign_id: str, date_from: Optional[str] = None, date_to: Optional[str] = None):
    """
    Fetch products for a campaign.

    Args:
        access_token: MercadoLibre API access token
        user_id: User ID for API calls
        advertiser_id: Advertiser ID
        campaign_id: Campaign ID
        date_from: Start date for data collection in YYYY-MM-DD format (optional, will use default if not provided)
        date_to: End date for data collection in YYYY-MM-DD format (optional, will use default if not provided)

    Returns:
        List of campaign products
    """
    # Use provided dates or fall back to defaults
    start_date = date_from if date_from else DATE_FROM
    end_date = date_to if date_to else DATE_TO

    async with MercadoLibreClient(access_token, user_id) as client:
        return await client.list_product_ads_items(
            advertiser_id,
            campaign_id,
            start_date,
            end_date
        )
